.layout-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.main-content {
  margin-top: 60px; /* Height of header */
  margin-left: 250px; /* Width of sidebar */
  flex: 1;
  overflow-y: auto;
  background: #f8f9fa;
}

.content-wrapper {
  padding: 20px;
  min-height: calc(100vh - 60px);
}

/* Responsive design */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
  }
  
  .content-wrapper {
    padding: 15px;
  }
}
