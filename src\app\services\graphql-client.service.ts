import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

// Interfaces para las respuestas del cliente GraphQL
export interface AuthResponse {
  success: boolean;
  message: string;
  token?: string;
}

export interface UserResponse {
  uuid: string;
  username: string;
  nombre: string;
  apellido: string;
  dni: string;
  telefono?: string;
  email?: string;
  role: string;
  estado: string;
}

export interface GetAllUsersResponse {
  success: boolean;
  message: string;
  users?: UserResponse[];
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  password: string;
  nombre: string;
  apellido: string;
  dni: string;
  telefono?: string;
  email?: string;
  role: string;
}

@Injectable({
  providedIn: 'root'
})
export class GraphqlClientService {
  private readonly baseUrl = 'http://localhost:8083/api/graphql-client';

  constructor(private http: HttpClient) {}

  /**
   * Login de usuario usando el cliente GraphQL
   */
  login(loginRequest: LoginRequest): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${this.baseUrl}/login`, loginRequest);
  }

  /**
   * Registro de usuario usando el cliente GraphQL
   */
  register(registerRequest: RegisterRequest): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${this.baseUrl}/register`, registerRequest);
  }

  /**
   * Obtener todos los usuarios usando el cliente GraphQL
   */
  getAllUsers(): Observable<GetAllUsersResponse> {
    return this.http.get<GetAllUsersResponse>(`${this.baseUrl}/users`);
  }
}
