import { HttpInterceptorFn } from '@angular/common/http';
import { inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

/**
 * Interceptor HTTP que agrega automáticamente el token de autenticación
 * a todas las requests que van al backend GraphQL client
 */
export const authInterceptor: HttpInterceptorFn = (req, next) => {
  const platformId = inject(PLATFORM_ID);
  
  // Solo ejecutar en el navegador
  if (!isPlatformBrowser(platformId)) {
    return next(req);
  }

  // Solo agregar token a requests del GraphQL client
  if (req.url.includes('/api/graphql-client')) {
    const token = localStorage.getItem('token');
    
    if (token) {
      console.log('🔐 Agregando token de autenticación a request:', req.url);
      
      // Clonar la request y agregar el header de autorización
      const authReq = req.clone({
        setHeaders: {
          Authorization: `<PERSON><PERSON> ${token}`
        }
      });
      
      return next(authReq);
    } else {
      console.warn('⚠️ No hay token disponible para request:', req.url);
    }
  }

  return next(req);
};
