package com.midas.graphqlcrm.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class SecurityConfig {

        @Value("${spring.jwks.uri}")
        private String jwksUri;

        @Bean
        SecurityFilterChain securityFilterChain(HttpSecurity http,
                        JwtDecoder jwtDecoder,
                        JwtAuthenticationConverter jwtAuthenticationConverter) throws Exception {

                // CORS no necesario - el frontend no se conecta directamente aquí
                http
                                .oauth2ResourceServer(resourceCustomizer -> resourceCustomizer
                                                .jwt(jwtCustomizer -> jwtCustomizer
                                                                .jwkSetUri(jwksUri)
                                                                .decoder(jwtDecoder)
                                                                .jwtAuthenticationConverter(
                                                                                jwtAuthenticationConverter)));

                http
                                .authorizeHttpRequests(auth -> auth.anyRequest().permitAll());

                http
                                .csrf(AbstractHttpConfigurer::disable);

                return http.build();
        }

        @Bean
        JwtDecoder jwtDecoder() {
                return NimbusJwtDecoder.withJwkSetUri(jwksUri).build();
        }

        @Bean
        JwtAuthenticationConverter jwtAuthenticationConverter() {
                JwtAuthenticationConverter jwtAuthenticationConverter = new JwtAuthenticationConverter();
                jwtAuthenticationConverter.setJwtGrantedAuthoritiesConverter(jwt -> {
                        System.out.println("🔍 Procesando JWT - Claims: " + jwt.getClaims());

                        List<String> authorities = jwt.getClaim("authorities");
                        System.out.println("🔑 Autoridades extraídas del JWT: " + authorities);

                        if (authorities == null || authorities.isEmpty()) {
                                System.out.println(
                                                "⚠️ No se encontraron autoridades en el JWT, usando autoridades por defecto");
                                authorities = List.of("read", "create", "update", "delete");
                        }

                        Collection<GrantedAuthority> grantedAuthorities = authorities.stream()
                                        .map(SimpleGrantedAuthority::new)
                                        .collect(Collectors.toList());

                        System.out.println("✅ Autoridades finales: " + grantedAuthorities);
                        return grantedAuthorities;
                });
                return jwtAuthenticationConverter;
        }

        @Bean
        public PasswordEncoder passwordEncoder() {
                return new BCryptPasswordEncoder();
        }
}
