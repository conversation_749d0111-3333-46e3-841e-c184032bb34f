package com.midas.crm.api;

import com.midas.crm.dto.request.LoginRequest;
import com.midas.crm.dto.request.RegisterRequest;
import com.midas.crm.dto.response.AuthResponse;
import com.midas.crm.dto.response.GetAllUsersResponse;
import com.midas.crm.service.GraphqlService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Controlador limpio para el cliente GraphQL
 * Solo incluye las funcionalidades principales: login, register y getAllUsers
 */
@RequestMapping(path = "/api/graphql-client")
@RestController
@RequiredArgsConstructor
public class GraphlController {

    private final GraphqlService service;

    /**
     * Endpoint para login de usuarios
     */
    @PostMapping(path = "/login")
    public ResponseEntity<AuthResponse> login(@RequestBody LoginRequest loginRequest) {
        return ResponseEntity.ok(service.login(loginRequest));
    }

    /**
     * Endpoint para registro de usuarios
     */
    @PostMapping(path = "/register")
    public ResponseEntity<AuthResponse> register(@RequestBody RegisterRequest registerRequest) {
        return ResponseEntity.ok(service.register(registerRequest));
    }

    /**
     * Endpoint para obtener todos los usuarios
     */
    @GetMapping(path = "/users")
    public ResponseEntity<GetAllUsersResponse> getAllUsers() {
        return ResponseEntity.ok(service.getAllUsers());
    }

}
