import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css'
})
export class DashboardComponent {
  stats = [
    {
      title: 'Total Usuarios',
      value: '150',
      icon: 'users',
      color: '#3498db'
    },
    {
      title: 'Clientes Activos',
      value: '89',
      icon: 'clients',
      color: '#2ecc71'
    },
    {
      title: 'Ventas del Mes',
      value: '$45,230',
      icon: 'sales',
      color: '#e74c3c'
    },
    {
      title: 'Tareas Pendientes',
      value: '23',
      icon: 'tasks',
      color: '#f39c12'
    }
  ];
}
