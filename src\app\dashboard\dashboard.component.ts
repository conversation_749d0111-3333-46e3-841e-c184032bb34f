import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './dashboard.component.html',
})
export class DashboardComponent {
  stats = [
    {
      title: 'Total Usuarios',
      value: '150',
      icon: 'users',
      color: 'bg-blue-500',
      change: '+12%',
    },
    {
      title: 'Clientes Activos',
      value: '89',
      icon: 'clients',
      color: 'bg-green-500',
      change: '+8%',
    },
    {
      title: 'Ventas del Mes',
      value: '$45,230',
      icon: 'sales',
      color: 'bg-purple-500',
      change: '+15%',
    },
    {
      title: 'Tareas Pendientes',
      value: '23',
      icon: 'tasks',
      color: 'bg-orange-500',
      change: '-5%',
    },
  ];
}
