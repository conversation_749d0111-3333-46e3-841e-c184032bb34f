import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ThemeService {
  private isDarkModeSubject = new BehaviorSubject<boolean>(false);
  public isDarkMode$ = this.isDarkModeSubject.asObservable();

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    this.initializeTheme();
  }

  private initializeTheme() {
    // Solo ejecutar en el navegador, no en SSR
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    // Verificar si hay una preferencia guardada
    const savedTheme = localStorage.getItem('theme');

    if (savedTheme) {
      this.setDarkMode(savedTheme === 'dark');
    } else {
      // Si no hay preferencia guardada, usar la preferencia del sistema
      const prefersDark = window.matchMedia(
        '(prefers-color-scheme: dark)'
      ).matches;
      this.setDarkMode(prefersDark);
    }

    // Escuchar cambios en la preferencia del sistema
    window
      .matchMedia('(prefers-color-scheme: dark)')
      .addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
          this.setDarkMode(e.matches);
        }
      });
  }

  toggleDarkMode() {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    const newMode = !this.isDarkModeSubject.value;
    this.setDarkMode(newMode);
    localStorage.setItem('theme', newMode ? 'dark' : 'light');
  }

  setDarkMode(isDark: boolean) {
    this.isDarkModeSubject.next(isDark);

    // Solo manipular el DOM en el navegador
    if (isPlatformBrowser(this.platformId)) {
      if (isDark) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
  }

  isDarkMode(): boolean {
    return this.isDarkModeSubject.value;
  }
}
