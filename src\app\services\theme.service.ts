import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private isDarkModeSubject = new BehaviorSubject<boolean>(false);
  public isDarkMode$ = this.isDarkModeSubject.asObservable();

  constructor() {
    this.initializeTheme();
  }

  private initializeTheme() {
    // Verificar si hay una preferencia guardada
    const savedTheme = localStorage.getItem('theme');
    
    if (savedTheme) {
      this.setDarkMode(savedTheme === 'dark');
    } else {
      // Si no hay preferencia guardada, usar la preferencia del sistema
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      this.setDarkMode(prefersDark);
    }

    // Escuchar cambios en la preferencia del sistema
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!localStorage.getItem('theme')) {
        this.setDarkMode(e.matches);
      }
    });
  }

  toggleDarkMode() {
    const newMode = !this.isDarkModeSubject.value;
    this.setDarkMode(newMode);
    localStorage.setItem('theme', newMode ? 'dark' : 'light');
  }

  setDarkMode(isDark: boolean) {
    this.isDarkModeSubject.next(isDark);
    
    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }

  isDarkMode(): boolean {
    return this.isDarkModeSubject.value;
  }
}
