#!/bin/bash

echo "🧹 Limpiando dependencias de Apollo GraphQL..."

# Eliminar node_modules y package-lock.json
rm -rf node_modules
rm -f package-lock.json

echo "📦 Instalando dependencias limpias..."

# Instalar dependencias
npm install

echo "✅ Limpieza completada!"
echo ""
echo "🚀 Para iniciar el frontend:"
echo "   ng serve"
echo ""
echo "📋 Dependencias eliminadas:"
echo "   - @apollo/client"
echo "   - apollo-angular" 
echo "   - graphql"
echo "   - graphql-ws"
echo ""
echo "✅ Ahora el frontend usa solo HTTP REST para comunicarse con el cliente GraphQL"
