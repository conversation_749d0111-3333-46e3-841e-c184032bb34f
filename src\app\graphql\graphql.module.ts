import { NgModule, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { provideHttpClient } from '@angular/common/http';
import { APOLLO_OPTIONS } from 'apollo-angular';
import { HttpLink } from 'apollo-angular/http';
import { ApolloClient, InMemoryCache } from '@apollo/client/core';

const httpUri = 'http://localhost:8080/graphql';

export function createApollo(
  httpLink: HttpLink,
  platformId: Object
): ApolloClient<any> {
  return new ApolloClient({
    link: httpLink.create({ uri: httpUri }),
    cache: new InMemoryCache(),
    // Configuración para SSR
    ssrMode: !isPlatformBrowser(platformId),
    defaultOptions: {
      watchQuery: {
        errorPolicy: 'all',
      },
      query: {
        errorPolicy: 'all',
      },
    },
  });
}

@NgModule({
  providers: [
    provideHttpClient(),
    {
      provide: APOLLO_OPTIONS,
      useFactory: createApollo,
      deps: [HttpLink, PLATFORM_ID],
    },
  ],
})
export class GraphQLModule {}
