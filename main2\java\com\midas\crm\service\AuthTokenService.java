package com.midas.crm.service;

import org.springframework.stereotype.Service;
import org.springframework.web.context.annotation.SessionScope;

/**
 * Servicio para manejar tokens de autenticación en la sesión
 */
@Service
@SessionScope
public class AuthTokenService {
    
    private String currentToken;
    
    /**
     * Almacena el token de autenticación
     */
    public void setToken(String token) {
        this.currentToken = token;
        System.out.println("Token almacenado en sesión: " + (token != null ? "***" + token.substring(Math.max(0, token.length() - 10)) : "null"));
    }
    
    /**
     * Obtiene el token de autenticación actual
     */
    public String getToken() {
        return currentToken;
    }
    
    /**
     * Verifica si hay un token válido
     */
    public boolean hasValidToken() {
        return currentToken != null && !currentToken.trim().isEmpty();
    }
    
    /**
     * Limpia el token (logout)
     */
    public void clearToken() {
        System.out.println("Token eliminado de sesión");
        this.currentToken = null;
    }
    
    /**
     * Obtiene el header de autorización formateado
     */
    public String getAuthorizationHeader() {
        if (hasValidToken()) {
            return "Bearer " + currentToken;
        }
        return null;
    }
}
