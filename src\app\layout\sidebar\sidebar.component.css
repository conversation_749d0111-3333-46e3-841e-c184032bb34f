.sidebar {
  background: #2c3e50;
  width: 250px;
  height: 100vh;
  position: fixed;
  top: 60px; /* Height of header */
  left: 0;
  z-index: 999;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.sidebar-nav {
  padding: 20px 0;
}

.menu-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.menu-item {
  margin-bottom: 5px;
}

.menu-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  color: #bdc3c7;
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.menu-link:hover {
  background: #34495e;
  color: #ecf0f1;
  border-left-color: #3498db;
}

.menu-link.active {
  background: #34495e;
  color: #3498db;
  border-left-color: #3498db;
}

.menu-link svg {
  flex-shrink: 0;
}

.menu-text {
  font-weight: 500;
  font-size: 0.95rem;
}

/* Scrollbar styling */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: #2c3e50;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #34495e;
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: #4a6741;
}
