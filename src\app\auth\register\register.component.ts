import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../auth.service';
import { RegisterRequest } from '../../services/graphql-client.service';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css'],
})
export class RegisterComponent {
  registerData: RegisterRequest = {
    username: '',
    password: '',
    nombre: '',
    apellido: '',
    dni: '',
    telefono: '',
    email: '',
    role: 'USER',
  };

  confirmPassword = '';
  isLoading = false;
  errorMessage = '';
  successMessage = '';

  constructor(private authService: AuthService, private router: Router) {}

  onSubmit() {
    if (this.registerData.password !== this.confirmPassword) {
      this.errorMessage = 'Las contraseñas no coinciden';
      return;
    }

    if (
      !this.registerData.username ||
      !this.registerData.password ||
      !this.registerData.nombre ||
      !this.registerData.apellido ||
      !this.registerData.dni
    ) {
      this.errorMessage = 'Por favor complete todos los campos obligatorios';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.authService.register(this.registerData).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          this.successMessage =
            response.message || 'Usuario registrado exitosamente';
          setTimeout(() => {
            this.router.navigate(['/login']);
          }, 2000);
        } else {
          this.errorMessage = response.message || 'Error en el registro';
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error de registro:', error);
        this.errorMessage = 'Error de conexión. Intente nuevamente.';
      },
    });
  }

  goToLogin() {
    this.router.navigate(['/login']);
  }
}
