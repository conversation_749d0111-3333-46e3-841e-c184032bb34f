package com.midas.graphqlcrm.api;

import com.midas.graphqlcrm.service.AuthService;
import com.zee.graphqlcrm.codegen.DgsConstants;
import com.zee.graphqlcrm.codegen.types.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.SchemaMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import reactor.core.publisher.Flux;

import java.util.List;

@Controller
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = { "http://localhost:4200", "http://127.0.0.1:4200" }, allowCredentials = "true")
public class UserController {

    private final AuthService authService;

    // Login y Register NO requieren autenticación
    @SchemaMapping(typeName = DgsConstants.MUTATION.TYPE_NAME, field = DgsConstants.MUTATION.Login)
    public LoginResponse login(@Argument(DgsConstants.MUTATION.LOGIN_INPUT_ARGUMENT.LoginInput) LoginInput input) {
        return authService.loginGraphQL(input);
    }

    @SchemaMapping(typeName = DgsConstants.MUTATION.TYPE_NAME, field = DgsConstants.MUTATION.Register)
    public RegisterResponse register(
            @Argument(DgsConstants.MUTATION.REGISTER_INPUT_ARGUMENT.UserInput) UserInput input) {
        return authService.registerGraphQL(input);
    }

    // Queries que SÍ requieren autenticación
    @PreAuthorize("hasAnyAuthority('read','create')")
    @SchemaMapping(typeName = DgsConstants.QUERY.TYPE_NAME, field = DgsConstants.QUERY.GetUserByUsername)
    public com.zee.graphqlcrm.codegen.types.User getUserByUsername(
            @Argument(DgsConstants.QUERY.GETUSERBYUSERNAME_INPUT_ARGUMENT.Username) String username) {
        return authService.getUserGraphQLDto(username);
    }

    @PreAuthorize("hasAnyAuthority('read','create')")
    @SchemaMapping(typeName = DgsConstants.QUERY.TYPE_NAME, field = DgsConstants.QUERY.GetAllUsers)
    public List<com.zee.graphqlcrm.codegen.types.User> getAllUsers() {
        return authService.getAllUsersGraphQL();
    }

    @PreAuthorize("hasAnyAuthority('read','create')")
    @SchemaMapping(typeName = DgsConstants.QUERY.TYPE_NAME, field = DgsConstants.QUERY.SearchUsers)
    public List<com.zee.graphqlcrm.codegen.types.User> searchUsers(
            @Argument(DgsConstants.QUERY.SEARCHUSERS_INPUT_ARGUMENT.Input) UserSearchInput input) {
        return authService.searchUsersGraphQL(input);
    }

    @PreAuthorize("hasAnyAuthority('read','create')")
    @SchemaMapping(typeName = DgsConstants.QUERY.TYPE_NAME, field = DgsConstants.QUERY.GetUserById)
    public com.zee.graphqlcrm.codegen.types.User getUserById(
            @Argument(DgsConstants.QUERY.GETUSERBYID_INPUT_ARGUMENT.Id) String id) {
        return authService.getUserByIdGraphQL(Long.valueOf(id));
    }

    @SchemaMapping(typeName = DgsConstants.SUBSCRIPTION.TYPE_NAME, field = DgsConstants.SUBSCRIPTION.UserCreated)
    public Flux<com.zee.graphqlcrm.codegen.types.User> userCreated() {
        return authService.userCreatedSink().asFlux(); // Nuevo método getter
    }

    @SchemaMapping(typeName = DgsConstants.SUBSCRIPTION.TYPE_NAME, field = DgsConstants.SUBSCRIPTION.UserUpdates)
    public Flux<com.zee.graphqlcrm.codegen.types.User> userUpdates() {
        return authService.userUpdatedSink().asFlux(); // Nuevo método getter
    }

    @SchemaMapping(typeName = "Subscription", field = "userListUpdates")
    public Flux<List<User>> userListUpdates() {
        return authService.userListFlux();
    }

}
