import { Component, OnInit, OnDestroy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { UserService } from '../user.service';
import { User } from '../user.model';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-user-list',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './user-list.component.html',
})
export class UserListComponent implements OnInit, OnDestroy {
  users: User[] = [];
  loading = true;
  error = '';
  private userSubscription: Subscription = new Subscription();

  constructor(private userService: UserService) {}

  ngOnInit() {
    this.loadUsers();
    this.subscribeToUserUpdates();

    // Iniciar WebSocket si el usuario ya está autenticado
    this.userService.startWebSocketConnection();
  }

  ngOnDestroy() {
    this.userSubscription.unsubscribe();
  }

  private loadUsers() {
    this.loading = true;
    this.error = '';

    this.userService.getAllUsers().subscribe({
      next: (response) => {
        this.users = response.getAllUsers || [];
        this.loading = false;
        console.log('✅ Usuarios cargados inicialmente:', this.users.length);
      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.error =
          'Error al cargar usuarios. Verifique que el servidor esté funcionando.';
        this.loading = false;
      },
    });
  }

  /**
   * Suscribe a las actualizaciones de usuarios en tiempo real
   */
  private subscribeToUserUpdates() {
    this.userSubscription.add(
      this.userService.getUsersObservable().subscribe({
        next: (users) => {
          if (users.length > 0) {
            this.users = users;
            this.loading = false;
            console.log(
              '🔄 Usuarios actualizados en tiempo real:',
              users.length
            );
          }
        },
        error: (error) => {
          console.error('❌ Error en subscripción de usuarios:', error);
        },
      })
    );
  }

  /**
   * Método para recargar la lista de usuarios manualmente
   */
  refreshUsers() {
    this.loadUsers();
  }

  getRoleColor(role: string): string {
    switch (role) {
      case 'ADMIN':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'COORDINADOR':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'ASESOR':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  }
}
