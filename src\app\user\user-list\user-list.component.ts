import { Component, OnInit } from '@angular/core';
import { UserService } from '../user.service';
import { User } from '../user.model';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-user-list',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './user-list.component.html',
})
export class UserListComponent implements OnInit {
  users: User[] = [];
  loading = true;
  error = '';

  constructor(private userService: UserService) {}

  ngOnInit() {
    this.loadUsers();
    this.subscribeToUpdates();
  }

  private loadUsers() {
    this.userService.getAllUsers().subscribe({
      next: (res) => {
        this.users = res.data?.getAllUsers || [];
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.error = 'Error al cargar usuarios';
        this.loading = false;
      },
    });
  }

  private subscribeToUpdates() {
    this.userService.userListUpdates().subscribe({
      next: (update) => {
        this.users = update.data?.userListUpdates || [];
      },
      error: (error) => {
        console.error('Error in subscription:', error);
      },
    });
  }

  getRoleColor(role: string): string {
    switch (role) {
      case 'ADMIN':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'COORDINADOR':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'ASESOR':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  }
}
