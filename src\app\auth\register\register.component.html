<div class="register-container">
  <div class="register-card">
    <div class="register-header">
      <h2>Registro de Usuario</h2>
      <p>Complete los datos para crear su cuenta</p>
    </div>

    <form (ngSubmit)="onSubmit()" #registerForm="ngForm" class="register-form">
      <!-- Mensajes de error y éxito -->
      <div *ngIf="errorMessage" class="alert alert-error">
        {{ errorMessage }}
      </div>
      
      <div *ngIf="successMessage" class="alert alert-success">
        {{ successMessage }}
      </div>

      <!-- Datos de acceso -->
      <div class="form-section">
        <h3>Datos de Acceso</h3>
        
        <div class="form-group">
          <label for="username">Usuario *</label>
          <input
            type="text"
            id="username"
            name="username"
            [(ngModel)]="registerData.username"
            required
            class="form-control"
            placeholder="Ingrese su nombre de usuario"
          />
        </div>

        <div class="form-group">
          <label for="password">Contraseña *</label>
          <input
            type="password"
            id="password"
            name="password"
            [(ngModel)]="registerData.password"
            required
            class="form-control"
            placeholder="Ingrese su contraseña"
          />
        </div>

        <div class="form-group">
          <label for="confirmPassword">Confirmar Contraseña *</label>
          <input
            type="password"
            id="confirmPassword"
            name="confirmPassword"
            [(ngModel)]="confirmPassword"
            required
            class="form-control"
            placeholder="Confirme su contraseña"
          />
        </div>
      </div>

      <!-- Datos personales -->
      <div class="form-section">
        <h3>Datos Personales</h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="nombre">Nombre *</label>
            <input
              type="text"
              id="nombre"
              name="nombre"
              [(ngModel)]="registerData.nombre"
              required
              class="form-control"
              placeholder="Ingrese su nombre"
            />
          </div>

          <div class="form-group">
            <label for="apellido">Apellido *</label>
            <input
              type="text"
              id="apellido"
              name="apellido"
              [(ngModel)]="registerData.apellido"
              required
              class="form-control"
              placeholder="Ingrese su apellido"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="dni">DNI *</label>
          <input
            type="text"
            id="dni"
            name="dni"
            [(ngModel)]="registerData.dni"
            required
            class="form-control"
            placeholder="Ingrese su DNI"
          />
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="telefono">Teléfono</label>
            <input
              type="tel"
              id="telefono"
              name="telefono"
              [(ngModel)]="registerData.telefono"
              class="form-control"
              placeholder="Ingrese su teléfono"
            />
          </div>

          <div class="form-group">
            <label for="email">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              [(ngModel)]="registerData.email"
              class="form-control"
              placeholder="Ingrese su email"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="role">Rol</label>
          <select
            id="role"
            name="role"
            [(ngModel)]="registerData.role"
            class="form-control"
          >
            <option value="USER">Usuario</option>
            <option value="ADMIN">Administrador</option>
            <option value="ASESOR">Asesor</option>
            <option value="COORDINADOR">Coordinador</option>
          </select>
        </div>
      </div>

      <!-- Botones -->
      <div class="form-actions">
        <button
          type="submit"
          [disabled]="!registerForm.form.valid || isLoading"
          class="btn btn-primary"
        >
          <span *ngIf="isLoading" class="loading-spinner"></span>
          {{ isLoading ? 'Registrando...' : 'Registrar' }}
        </button>

        <button
          type="button"
          (click)="goToLogin()"
          class="btn btn-secondary"
        >
          Volver al Login
        </button>
      </div>
    </form>
  </div>
</div>
