<header
  class="bg-gradient-to-r from-blue-600 to-purple-600 dark:from-gray-800 dark:to-gray-900 text-white shadow-lg fixed top-0 left-0 right-0 z-50 h-16"
>
  <div class="flex justify-between items-center h-full px-6">
    <!-- Logo -->
    <div class="flex items-center">
      <h1 class="text-xl font-bold">MIDAS CRM</h1>
    </div>

    <!-- Right side controls -->
    <div class="flex items-center space-x-4">
      <!-- User info -->
      <div
        *ngIf="currentUser$ | async as user"
        class="hidden md:flex flex-col items-end text-sm"
      >
        <span class="font-semibold">{{ user.nombre }} {{ user.apellido }}</span>
        <span class="text-xs opacity-80">{{ user.role }}</span>
      </div>

      <!-- Dark mode toggle -->
      <button
        (click)="toggleDarkMode()"
        class="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200"
        title="Cambiar tema"
      >
        <!-- Sun icon (light mode) -->
        <svg
          *ngIf="!(isDarkMode$ | async)"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <circle cx="12" cy="12" r="5"></circle>
          <line x1="12" y1="1" x2="12" y2="3"></line>
          <line x1="12" y1="21" x2="12" y2="23"></line>
          <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
          <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
          <line x1="1" y1="12" x2="3" y2="12"></line>
          <line x1="21" y1="12" x2="23" y2="12"></line>
          <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
          <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
        </svg>

        <!-- Moon icon (dark mode) -->
        <svg
          *ngIf="isDarkMode$ | async"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
        </svg>
      </button>

      <!-- Logout button -->
      <button
        (click)="logout()"
        class="flex items-center space-x-2 px-3 py-2 rounded-lg bg-red-500/20 hover:bg-red-500/30 transition-colors duration-200"
        title="Cerrar sesión"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
          <polyline points="16,17 21,12 16,7"></polyline>
          <line x1="21" y1="12" x2="9" y2="12"></line>
        </svg>
        <span class="hidden sm:inline">Salir</span>
      </button>
    </div>
  </div>
</header>
