package com.midas.crm.query;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @code @created : 28 Nov., 2024
 */
public interface QueryDetails {


    String EMPLOYEE_PAGINATION = """
                query employeePagination(
                    $input: EmployeeSearchInput
                    $page: Int
                    $size: Int
                ) {
                    employeePagination(
                        search: $input
                        page: $page
                        size: $size
                    ) {
                        page
                        size
                        totalPage
                        totalElement
                        employeeConnection {
                            edges {
                                node {
                                    uuid
                                    name
                                    dateOfBirth
                                    gender
                                    salary
                                    role
                                    age
                                    active
                                    employeeId
                                }
                            }
                        }
                    }
                }
            """;

    String LOGIN_MUTATION = """
                mutation Login($loginInput: LoginInput!) {
                    login(loginInput: $loginInput) {
                        success
                        message
                        token
                        user {
                            uuid
                            username
                            nombre
                            apellido
                            email
                        }
                    }
                }
            """;

    String REGISTER_MUTATION = """
                mutation Register($userInput: UserInput!) {
                    register(userInput: $userInput) {
                        success
                        message
                        user {
                            uuid
                            username
                            nombre
                            apellido
                            email
                        }
                    }
                }
            """;

    String GET_ALL_USERS_QUERY = """
                query GetAllUsers {
                    getAllUsers {
                        uuid
                        username
                        nombre
                        apellido
                        dni
                        telefono
                        email
                        role
                        estado
                        fechaCreacion
                    }
                }
            """;

    String ASIGNAR_ASESORES_MUTATION = """
                mutation AsignarAsesores($coordinadorId: ID!, $asesorIds: [ID!]!) {
                    asignarAsesores(coordinadorId: $coordinadorId, asesorIds: $asesorIds)
                }
            """;

    String OBTENER_ASESORES_COORDINADOR_QUERY = """
                query AsesoresDeCoordinador($coordinadorId: ID!) {
                    asesoresDeCoordinador(coordinadorId: $coordinadorId) {
                        uuid
                        username
                        nombre
                        apellido
                        dni
                        email
                        telefono
                        estado
                    }
                }
            """;

    String OBTENER_ASESORES_SIN_COORDINADOR_QUERY = """
                query AsesoresSinCoordinador {
                    asesoresSinCoordinador {
                        uuid
                        username
                        nombre
                        apellido
                        dni
                        email
                        telefono
                        estado
                    }
                }
            """;

}
