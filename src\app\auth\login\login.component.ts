import { Component } from '@angular/core';
import { AuthService } from '../auth.service';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './login.component.html',
})
export class LoginComponent {
  username = '';
  password = '';
  error = '';
  loading = false;

  constructor(private authService: AuthService, private router: Router) {
    // Si ya está autenticado, redirigir al dashboard
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/dashboard']);
    }
  }

  login() {
    if (!this.username || !this.password) {
      this.error = 'Por favor ingrese usuario y contraseña';
      return;
    }

    this.loading = true;
    this.error = '';

    this.authService.login(this.username, this.password).subscribe({
      next: (result) => {
        this.loading = false;
        if (result.login.success) {
          this.router.navigate(['/dashboard']);
        } else {
          this.error = result.login.message;
        }
      },
      error: (error) => {
        this.loading = false;
        console.error('Error de login:', error);
        this.error = 'Error de conexión. Verifique su conexión a internet.';
      },
    });
  }

  onSubmit(event: Event) {
    event.preventDefault();
    this.login();
  }
}
