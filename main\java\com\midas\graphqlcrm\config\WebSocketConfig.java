package com.midas.graphqlcrm.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.graphql.server.webmvc.GraphQlWebMvcConfigurer;
import org.springframework.graphql.server.WebGraphQlHandler;
import org.springframework.graphql.server.support.GraphQlWebSocketMessageHandler;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * Configuración WebSocket para subscripciones GraphQL
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer, GraphQlWebMvcConfigurer {

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // Registrar el handler WebSocket para GraphQL subscriptions
        registry.addHandler(new GraphQlWebSocketMessageHandler(webGraphQlHandler()), "/subscription")
                .setAllowedOrigins("http://localhost:4200") // Permitir frontend Angular
                .withSockJS(); // Habilitar SockJS como fallback
    }

    @Bean
    public WebGraphQlHandler webGraphQlHandler() {
        // Este bean será inyectado automáticamente por Spring GraphQL
        return WebGraphQlHandler.builder()
                .build();
    }

    /**
     * Configuración CORS específica para WebSocket
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSourceWebSocket() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // Permitir el frontend Angular
        configuration.setAllowedOrigins(Arrays.asList("http://localhost:4200"));
        
        // Permitir todos los métodos
        configuration.setAllowedMethods(Arrays.asList("*"));
        
        // Permitir todos los headers
        configuration.setAllowedHeaders(Arrays.asList("*"));
        
        // Permitir credenciales
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/subscription/**", configuration);
        
        return source;
    }
}
