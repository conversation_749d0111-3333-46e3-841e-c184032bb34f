import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { ThemeService } from './services/theme.service';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css',
})
export class AppComponent implements OnInit {
  title = 'graphql-crm-client';

  constructor(private themeService: ThemeService) {}

  ngOnInit() {
    // El servicio de tema se inicializa automáticamente
  }
}
