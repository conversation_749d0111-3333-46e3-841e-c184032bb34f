import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { User } from './user.model';
import {
  GraphqlClientService,
  UserResponse,
} from '../services/graphql-client.service';

@Injectable({ providedIn: 'root' })
export class UserService {
  constructor(private graphqlClientService: GraphqlClientService) {}

  getAllUsers(): Observable<{ getAllUsers: User[] }> {
    return this.graphqlClientService.getAllUsers().pipe(
      map((response) => {
        if (response.success && response.users) {
          // Convertir UserResponse[] a User[]
          const users: User[] = response.users.map(
            (userResponse: UserResponse) => ({
              id: userResponse.id,
              username: userResponse.username,
              nombre: userResponse.nombre,
              apellido: userResponse.apellido,
              dni: userResponse.dni,
              telefono: userResponse.telefono || '',
              email: userResponse.email || '',
              role: userResponse.role as 'ASESOR' | 'ADMIN' | 'COORDINADOR',
              estado: userResponse.estado,
              fechaCreacion: new Date().toISOString(), // Valor por defecto
              fechaCese: undefined, // Valor por defecto
            })
          );

          return { getAllUsers: users };
        } else {
          // En caso de error, retornar array vacío
          console.error('Error al obtener usuarios:', response.message);
          return { getAllUsers: [] };
        }
      })
    );
  }

  // Método de suscripción eliminado - no necesario con REST API
}
