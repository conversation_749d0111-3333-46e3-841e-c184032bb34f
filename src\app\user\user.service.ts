import { Injectable } from '@angular/core';
import { Apollo, gql } from 'apollo-angular';
import { User } from './user.model';

@Injectable({ providedIn: 'root' })
export class UserService {
  constructor(private apollo: Apollo) {}

  getAllUsers() {
    return this.apollo.query<{ getAllUsers: User[] }>({
      query: gql`
        query {
          getAllUsers {
            id
            username
            nombre
            apellido
            dni
            telefono
            email
            fechaCreacion
            fechaCese
            estado
            role
          }
        }
      `,
    });
  }

  userListUpdates() {
    return this.apollo.subscribe<{ userListUpdates: User[] }>({
      query: gql`
        subscription {
          userListUpdates {
            id
            username
            nombre
            apellido
            dni
            telefono
            email
            fechaCreacion
            fechaCese
            estado
            role
          }
        }
      `,
    });
  }
}
