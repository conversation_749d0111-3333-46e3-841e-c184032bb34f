import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { map } from 'rxjs/operators';
import { User } from './user.model';
import {
  GraphqlClientService,
  UserResponse,
} from '../services/graphql-client.service';

@Injectable({ providedIn: 'root' })
export class UserService {
  private usersSubject = new BehaviorSubject<User[]>([]);
  public users$ = this.usersSubject.asObservable();
  private wsConnection: WebSocket | null = null;

  constructor(private graphqlClientService: GraphqlClientService) {
    // WebSocket se iniciará solo después del login exitoso
  }

  getAllUsers(): Observable<{ getAllUsers: User[] }> {
    return this.graphqlClientService.getAllUsers().pipe(
      map((response) => {
        if (response.success && response.users) {
          // Convertir UserResponse[] a User[]
          const users: User[] = response.users.map(
            (userResponse: UserResponse) => ({
              id: userResponse.id,
              username: userResponse.username,
              nombre: userResponse.nombre,
              apellido: userResponse.apellido,
              dni: userResponse.dni,
              telefono: userResponse.telefono || '',
              email: userResponse.email || '',
              role: userResponse.role as 'ASESOR' | 'ADMIN' | 'COORDINADOR',
              estado: userResponse.estado,
              fechaCreacion: new Date().toISOString(), // Valor por defecto
              fechaCese: undefined, // Valor por defecto
            })
          );

          // Actualizar el BehaviorSubject con los usuarios obtenidos
          this.usersSubject.next(users);
          return { getAllUsers: users };
        } else {
          // En caso de error, retornar array vacío
          console.error('Error al obtener usuarios:', response.message);
          return { getAllUsers: [] };
        }
      })
    );
  }

  /**
   * Inicializa la conexión WebSocket para subscripciones GraphQL
   */
  private initializeWebSocketConnection(): void {
    try {
      // URL del WebSocket GraphQL (puerto 8080 del backend main1)
      const wsUrl = 'ws://localhost:8080/subscription';
      this.wsConnection = new WebSocket(wsUrl, 'graphql-ws');

      this.wsConnection.onopen = () => {
        console.log(
          '🔗 Conexión WebSocket establecida para subscripciones GraphQL'
        );
        this.subscribeToUserUpdates();
      };

      this.wsConnection.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleWebSocketMessage(message);
        } catch (error) {
          console.error('❌ Error al parsear mensaje WebSocket:', error);
        }
      };

      this.wsConnection.onerror = (error) => {
        console.error('❌ Error en conexión WebSocket:', error);
      };

      this.wsConnection.onclose = (event) => {
        console.log('🔌 Conexión WebSocket cerrada:', event.code, event.reason);

        // Solo reintentar si no fue un cierre manual (código 1000)
        if (event.code !== 1000 && this.wsConnection !== null) {
          console.log('⏳ Reintentando conexión WebSocket en 10 segundos...');
          setTimeout(() => {
            if (this.wsConnection !== null) {
              // Verificar que no se haya cerrado manualmente
              this.initializeWebSocketConnection();
            }
          }, 10000);
        }
      };
    } catch (error) {
      console.error('❌ Error al inicializar WebSocket:', error);
    }
  }

  /**
   * Suscribe a las actualizaciones de usuarios vía WebSocket
   */
  private subscribeToUserUpdates(): void {
    if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
      const subscriptionMessage = {
        id: 'user-list-subscription',
        type: 'start',
        payload: {
          query: `
            subscription {
              userListUpdates {
                id
                username
                nombre
                apellido
                dni
                telefono
                email
                role
                estado
                fechaCreacion
              }
            }
          `,
        },
      };

      this.wsConnection.send(JSON.stringify(subscriptionMessage));
      console.log('📡 Suscripción a userListUpdates enviada');
    }
  }

  /**
   * Maneja los mensajes recibidos del WebSocket
   */
  private handleWebSocketMessage(message: any): void {
    if (message.type === 'data' && message.payload?.data?.userListUpdates) {
      const updatedUsers: User[] = message.payload.data.userListUpdates.map(
        (user: any) => ({
          id: user.id,
          username: user.username,
          nombre: user.nombre,
          apellido: user.apellido,
          dni: user.dni,
          telefono: user.telefono || '',
          email: user.email || '',
          role: user.role as 'ASESOR' | 'ADMIN' | 'COORDINADOR',
          estado: user.estado,
          fechaCreacion: user.fechaCreacion || new Date().toISOString(),
          fechaCese: undefined,
        })
      );

      // Actualizar la lista de usuarios en tiempo real
      this.usersSubject.next(updatedUsers);
      console.log(
        '🔄 Lista de usuarios actualizada en tiempo real:',
        updatedUsers.length,
        'usuarios'
      );
    }
  }

  /**
   * Obtiene el observable de usuarios para suscribirse a cambios en tiempo real
   */
  getUsersObservable(): Observable<User[]> {
    return this.users$;
  }

  /**
   * Inicia la conexión WebSocket después del login exitoso
   */
  startWebSocketConnection(): void {
    if (
      !this.wsConnection ||
      this.wsConnection.readyState === WebSocket.CLOSED
    ) {
      console.log('🚀 Iniciando conexión WebSocket después del login...');
      this.initializeWebSocketConnection();
    }
  }

  /**
   * Cierra la conexión WebSocket al hacer logout
   */
  stopWebSocketConnection(): void {
    if (this.wsConnection) {
      console.log('🔌 Cerrando conexión WebSocket...');
      this.wsConnection.close();
      this.wsConnection = null;
    }
  }
}
