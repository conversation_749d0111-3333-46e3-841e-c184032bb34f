<div class="login-container">
  <div class="login-card">
    <div class="login-header">
      <h1 class="login-title">MIDAS CRM</h1>
      <p class="login-subtitle">Ingrese sus credenciales para acceder</p>
    </div>

    <form class="login-form" (ngSubmit)="onSubmit($event)">
      <div class="form-group">
        <label class="form-label" for="username">Usuario</label>
        <input
          id="username"
          type="text"
          class="form-input"
          [(ngModel)]="username"
          name="username"
          placeholder="Ingrese su usuario"
          required
          [disabled]="loading"
        />
      </div>

      <div class="form-group">
        <label class="form-label" for="password">Contraseña</label>
        <input
          id="password"
          type="password"
          class="form-input"
          [(ngModel)]="password"
          name="password"
          placeholder="Ingrese su contraseña"
          required
          [disabled]="loading"
        />
      </div>

      <button
        type="submit"
        class="login-button"
        [disabled]="loading || !username || !password"
      >
        <div *ngIf="loading" class="loading-spinner"></div>
        <span>{{ loading ? 'Iniciando sesión...' : 'Iniciar Sesión' }}</span>
      </button>

      <div *ngIf="error" class="error-message">
        {{ error }}
      </div>
    </form>
  </div>
</div>
  