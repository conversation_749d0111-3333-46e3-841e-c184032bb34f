<div
  class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600 dark:from-gray-900 dark:to-gray-800 p-4"
>
  <div
    class="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-2xl w-full max-w-md"
  >
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
        MIDAS CRM
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        Ingrese sus credenciales para acceder
      </p>
    </div>

    <form class="space-y-6" (ngSubmit)="onSubmit($event)">
      <div>
        <label
          for="username"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          Usuario
        </label>
        <input
          id="username"
          type="text"
          class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors"
          [(ngModel)]="username"
          name="username"
          placeholder="Ingrese su usuario"
          required
          [disabled]="loading"
        />
      </div>

      <div>
        <label
          for="password"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          Contraseña
        </label>
        <input
          id="password"
          type="password"
          class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors"
          [(ngModel)]="password"
          name="password"
          placeholder="Ingrese su contraseña"
          required
          [disabled]="loading"
        />
      </div>

      <button
        type="submit"
        class="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2"
        [disabled]="loading || !username || !password"
      >
        <div
          *ngIf="loading"
          class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"
        ></div>
        <span>{{ loading ? "Iniciando sesión..." : "Iniciar Sesión" }}</span>
      </button>

      <div
        *ngIf="error"
        class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg text-sm"
      >
        {{ error }}
      </div>
    </form>
  </div>
</div>
