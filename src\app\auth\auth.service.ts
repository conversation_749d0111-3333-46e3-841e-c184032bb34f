import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import {
  GraphqlClientService,
  AuthResponse,
  LoginRequest,
  RegisterRequest,
} from '../services/graphql-client.service';

export interface UserInfo {
  id: string;
  username: string;
  nombre: string;
  apellido: string;
  dni: string;
  telefono?: string;
  email?: string;
  role: 'COORDINADOR' | 'ASESOR' | 'ADMIN';
  estado: string;
}

export interface LoginResponse {
  login: {
    success: boolean;
    message: string;
    token?: string;
    user?: UserInfo;
  };
}

@Injectable({ providedIn: 'root' })
export class AuthService {
  private currentUserSubject = new BehaviorSubject<UserInfo | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(
    private graphqlClientService: GraphqlClientService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    // Verificar si hay un usuario logueado al inicializar
    this.checkStoredUser();
  }

  private checkStoredUser() {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    const token = localStorage.getItem('token');
    const userInfo = localStorage.getItem('userInfo');

    if (token && userInfo) {
      try {
        const user = JSON.parse(userInfo);
        this.currentUserSubject.next(user);
      } catch (error) {
        console.error('Error parsing stored user info:', error);
        this.logout();
      }
    }
  }

  login(username: string, password: string): Observable<LoginResponse> {
    const loginRequest: LoginRequest = { username, password };

    return this.graphqlClientService.login(loginRequest).pipe(
      tap((response: AuthResponse) => {
        if (response.success && response.token) {
          // Guardar token primero
          if (isPlatformBrowser(this.platformId)) {
            localStorage.setItem('token', response.token);
          }

          console.log(
            '✅ Login exitoso - Token OAuth2 guardado:',
            response.token.substring(0, 20) + '...'
          );

          // Obtener datos completos del usuario después del login
          this.loadUserDataAfterLogin(username);
        }
      }),
      map((response: AuthResponse) => {
        // Convertir AuthResponse a LoginResponse para mantener compatibilidad
        return {
          login: {
            success: response.success,
            message: response.message,
            token: response.token,
            user: response.success ? this.currentUserSubject.value : undefined,
          },
        } as LoginResponse;
      })
    );
  }

  register(registerData: RegisterRequest): Observable<AuthResponse> {
    return this.graphqlClientService.register(registerData);
  }

  logout() {
    if (isPlatformBrowser(this.platformId)) {
      localStorage.removeItem('token');
      localStorage.removeItem('userInfo');
    }
    this.currentUserSubject.next(null);
  }

  isAuthenticated(): boolean {
    if (!isPlatformBrowser(this.platformId)) {
      return false;
    }
    return !!localStorage.getItem('token');
  }

  getCurrentUser(): UserInfo | null {
    return this.currentUserSubject.value;
  }

  getToken(): string | null {
    if (!isPlatformBrowser(this.platformId)) {
      return null;
    }
    return localStorage.getItem('token');
  }

  /**
   * Carga los datos completos del usuario después del login exitoso
   */
  private loadUserDataAfterLogin(username: string): void {
    // Obtener los datos del usuario desde el backend
    this.graphqlClientService.getAllUsers().subscribe({
      next: (usersResponse) => {
        if (usersResponse.success && usersResponse.users) {
          // Buscar el usuario que acaba de hacer login
          const loggedUser = usersResponse.users.find(
            (user) => user.username === username
          );

          if (loggedUser) {
            const userInfo: UserInfo = {
              id: loggedUser.id.toString(),
              username: loggedUser.username,
              nombre: loggedUser.nombre,
              apellido: loggedUser.apellido,
              dni: loggedUser.dni,
              telefono: loggedUser.telefono || '',
              email: loggedUser.email,
              role: loggedUser.role,
              estado: loggedUser.estado,
            };

            // Guardar información completa del usuario
            if (isPlatformBrowser(this.platformId)) {
              localStorage.setItem('userInfo', JSON.stringify(userInfo));
            }

            // Actualizar el subject con datos completos
            this.currentUserSubject.next(userInfo);
            console.log('✅ Datos completos del usuario cargados:', userInfo);
          } else {
            console.warn('⚠️ Usuario no encontrado en la lista de usuarios');
          }
        }
      },
      error: (error) => {
        console.error('❌ Error al cargar datos del usuario:', error);
        // Crear usuario básico como fallback
        const basicUserInfo: UserInfo = {
          id: '0',
          username: username,
          nombre: '',
          apellido: '',
          dni: '',
          telefono: '',
          email: '',
          role: 'ASESOR',
          estado: 'ACTIVO',
        };
        this.currentUserSubject.next(basicUserInfo);
      },
    });
  }
}
