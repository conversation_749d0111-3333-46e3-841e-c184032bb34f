import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { Apollo, gql } from 'apollo-angular';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface UserInfo {
  id: string;
  username: string;
  nombre: string;
  apellido: string;
  dni: string;
  telefono?: string;
  email?: string;
  role: 'COORDINADOR' | 'ASESOR' | 'ADMIN';
  estado: string;
}

export interface LoginResponse {
  login: {
    success: boolean;
    message: string;
    token?: string;
    user?: UserInfo;
  };
}

@Injectable({ providedIn: 'root' })
export class AuthService {
  private currentUserSubject = new BehaviorSubject<UserInfo | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(
    private apollo: Apollo,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    // Verificar si hay un usuario logueado al inicializar
    this.checkStoredUser();
  }

  private checkStoredUser() {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    const token = localStorage.getItem('token');
    const userInfo = localStorage.getItem('userInfo');

    if (token && userInfo) {
      try {
        const user = JSON.parse(userInfo);
        this.currentUserSubject.next(user);
      } catch (error) {
        console.error('Error parsing stored user info:', error);
        this.logout();
      }
    }
  }

  login(username: string, password: string): Observable<LoginResponse> {
    return this.apollo
      .mutate<LoginResponse>({
        mutation: gql`
          mutation Login($loginInput: LoginInput!) {
            login(loginInput: $loginInput) {
              success
              message
              token
              user {
                id
                username
                nombre
                apellido
                dni
                telefono
                email
                role
                estado
              }
            }
          }
        `,
        variables: {
          loginInput: { username, password },
        },
      })
      .pipe(
        map((result) => {
          if (
            result.data?.login.success &&
            result.data.login.token &&
            result.data.login.user
          ) {
            // Guardar token y información del usuario solo en el navegador
            if (isPlatformBrowser(this.platformId)) {
              localStorage.setItem('token', result.data.login.token);
              localStorage.setItem(
                'userInfo',
                JSON.stringify(result.data.login.user)
              );
            }

            // Actualizar el subject
            this.currentUserSubject.next(result.data.login.user);
          }
          return result.data!;
        })
      );
  }

  logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('userInfo');
    this.currentUserSubject.next(null);
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem('token');
  }

  getCurrentUser(): UserInfo | null {
    return this.currentUserSubject.value;
  }

  getToken(): string | null {
    return localStorage.getItem('token');
  }
}
