import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import {
  GraphqlClientService,
  AuthResponse,
  LoginRequest,
  RegisterRequest,
} from '../services/graphql-client.service';

export interface UserInfo {
  id: string;
  username: string;
  nombre: string;
  apellido: string;
  dni: string;
  telefono?: string;
  email?: string;
  role: 'COORDINADOR' | 'ASESOR' | 'ADMIN';
  estado: string;
}

export interface LoginResponse {
  login: {
    success: boolean;
    message: string;
    token?: string;
    user?: UserInfo;
  };
}

@Injectable({ providedIn: 'root' })
export class AuthService {
  private currentUserSubject = new BehaviorSubject<UserInfo | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(
    private graphqlClientService: GraphqlClientService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    // Verificar si hay un usuario logueado al inicializar
    this.checkStoredUser();
  }

  private checkStoredUser() {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    const token = localStorage.getItem('token');
    const userInfo = localStorage.getItem('userInfo');

    if (token && userInfo) {
      try {
        const user = JSON.parse(userInfo);
        this.currentUserSubject.next(user);
      } catch (error) {
        console.error('Error parsing stored user info:', error);
        this.logout();
      }
    }
  }

  login(username: string, password: string): Observable<LoginResponse> {
    const loginRequest: LoginRequest = { username, password };

    return this.graphqlClientService.login(loginRequest).pipe(
      tap((response: AuthResponse) => {
        if (response.success && response.token) {
          // Crear UserInfo desde la respuesta del cliente GraphQL
          const userInfo: UserInfo = {
            id: username, // Usar username como id temporal
            username: username,
            nombre: '', // Estos campos se pueden obtener después
            apellido: '',
            dni: '',
            telefono: '',
            email: '',
            role: 'USER' as any, // Valor por defecto
            estado: 'ACTIVO',
          };

          // Guardar token y información del usuario solo en el navegador
          if (isPlatformBrowser(this.platformId)) {
            localStorage.setItem('token', response.token);
            localStorage.setItem('userInfo', JSON.stringify(userInfo));
          }

          // Actualizar el subject
          this.currentUserSubject.next(userInfo);
        }
      }),
      map((response: AuthResponse) => {
        // Convertir AuthResponse a LoginResponse para mantener compatibilidad
        return {
          login: {
            success: response.success,
            message: response.message,
            token: response.token,
            user: response.success ? this.currentUserSubject.value : undefined,
          },
        } as LoginResponse;
      })
    );
  }

  register(registerData: RegisterRequest): Observable<AuthResponse> {
    return this.graphqlClientService.register(registerData);
  }

  logout() {
    if (isPlatformBrowser(this.platformId)) {
      localStorage.removeItem('token');
      localStorage.removeItem('userInfo');
    }
    this.currentUserSubject.next(null);
  }

  isAuthenticated(): boolean {
    if (!isPlatformBrowser(this.platformId)) {
      return false;
    }
    return !!localStorage.getItem('token');
  }

  getCurrentUser(): UserInfo | null {
    return this.currentUserSubject.value;
  }

  getToken(): string | null {
    if (!isPlatformBrowser(this.platformId)) {
      return null;
    }
    return localStorage.getItem('token');
  }
}
