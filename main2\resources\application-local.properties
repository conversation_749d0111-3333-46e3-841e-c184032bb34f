spring.application.name=crm

# Database Configuration
spring.datasource.url=***************************************
spring.datasource.username=root
spring.datasource.password=
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect

# Server Configuration
server.port=8083

# GraphQL-CRM Configuration (GraphQL Nativo)
graphql.crm.url=http://localhost:8080
