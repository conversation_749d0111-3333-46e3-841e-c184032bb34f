import { Component } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';

interface MenuItem {
  label: string;
  route: string;
  icon: string;
  active?: boolean;
}

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.css'
})
export class SidebarComponent {
  menuItems: MenuItem[] = [
    {
      label: 'Dashboard',
      route: '/dashboard',
      icon: 'dashboard'
    },
    {
      label: 'Usuarios',
      route: '/users',
      icon: 'users'
    },
    {
      label: 'Clientes',
      route: '/clients',
      icon: 'clients'
    },
    {
      label: 'Reportes',
      route: '/reports',
      icon: 'reports'
    },
    {
      label: 'Configuración',
      route: '/settings',
      icon: 'settings'
    }
  ];

  constructor(private router: Router) {}

  isActiveRoute(route: string): boolean {
    return this.router.url === route;
  }
}
