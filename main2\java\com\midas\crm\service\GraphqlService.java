package com.midas.crm.service;

import com.midas.crm.dto.request.LoginRequest;
import com.midas.crm.dto.request.RegisterRequest;
import com.midas.crm.dto.response.AuthResponse;
import com.midas.crm.dto.response.GetAllUsersResponse;
import com.midas.crm.dto.response.UserResponse;
import com.midas.crm.query.QueryDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.RestClientException;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.http.HttpHeaders;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Base64;
import java.util.List;
import java.util.Map;

@Service
public class GraphqlService {

        @Value("${graphql.crm.url}")
        private String graphqlCrmUrl;

        private final RestClient restClient;

        @Autowired
        private AuthTokenService authTokenService;

        public GraphqlService() {
                this.restClient = RestClient.create();
        }

        /**
         * Método de login mejorado y limpio
         */

        public AuthResponse login(LoginRequest loginRequest) {
                try {
                        System.out.println("Conectando a GraphQL nativo en: " + graphqlCrmUrl);

                        Map<String, Object> variables = Map.of(
                                        "loginInput", Map.of(
                                                        "username", loginRequest.getUsername(),
                                                        "password", loginRequest.getPassword()));

                        Map<String, Object> requestBody = Map.of(
                                        "query", QueryDetails.LOGIN_MUTATION,
                                        "variables", variables);

                        @SuppressWarnings("unchecked")
                        Map<String, Object> response = restClient
                                        .post()
                                        .uri(graphqlCrmUrl + "/graphql")
                                        .body(requestBody)
                                        .header("Content-Type", "application/json")
                                        .retrieve()
                                        .body(Map.class);

                        AuthResponse authResponse = extractAuthResponse(response, "login");

                        // Si el login es exitoso, obtener token OAuth2 real y reemplazarlo
                        if (authResponse.isSuccess()) {
                                String oauth2Token = getOAuth2Token();
                                if (oauth2Token != null) {
                                        authResponse.setToken(oauth2Token); // Reemplazar con token OAuth2 real
                                        authTokenService.setToken(oauth2Token);
                                        System.out.println("✅ Login exitoso - Token OAuth2 obtenido y almacenado");
                                } else {
                                        System.out.println("⚠️ Login exitoso pero no se pudo obtener token OAuth2");
                                }
                        }

                        return authResponse;

                } catch (RestClientException e) {
                        System.err.println("Error de conexión GraphQL: " + e.getMessage());
                        return createErrorResponse("Error de conexión con el servidor GraphQL: " + e.getMessage());
                } catch (Exception e) {
                        System.err.println("Error inesperado en login: " + e.getMessage());
                        e.printStackTrace();
                        return createErrorResponse("Error inesperado: " + e.getMessage());
                }
        }

        /**
         * Método de register mejorado y limpio
         */
        public AuthResponse register(RegisterRequest registerRequest) {
                try {
                        System.out.println("Registrando usuario en GraphQL nativo: " + graphqlCrmUrl);

                        Map<String, Object> variables = Map.of(
                                        "userInput", Map.of(
                                                        "username", registerRequest.getUsername(),
                                                        "password", registerRequest.getPassword(),
                                                        "nombre", registerRequest.getNombre(),
                                                        "apellido", registerRequest.getApellido(),
                                                        "dni", registerRequest.getDni(),
                                                        "telefono", registerRequest.getTelefono(),
                                                        "email", registerRequest.getEmail(),
                                                        "role", registerRequest.getRole()));

                        Map<String, Object> requestBody = Map.of(
                                        "query", QueryDetails.REGISTER_MUTATION,
                                        "variables", variables);

                        @SuppressWarnings("unchecked")
                        Map<String, Object> response = restClient
                                        .post()
                                        .uri(graphqlCrmUrl + "/graphql")
                                        .body(requestBody)
                                        .header("Content-Type", "application/json")
                                        .retrieve()
                                        .body(Map.class);

                        return extractAuthResponse(response, "register");

                } catch (RestClientException e) {
                        System.err.println("Error de conexión GraphQL en register: " + e.getMessage());
                        return createErrorResponse("Error de conexión con el servidor GraphQL: " + e.getMessage());
                } catch (Exception e) {
                        System.err.println("Error inesperado en register: " + e.getMessage());
                        e.printStackTrace();
                        return createErrorResponse("Error inesperado: " + e.getMessage());
                }
        }

        /**
         * Método para obtener todos los usuarios (requiere autenticación)
         */
        public GetAllUsersResponse getAllUsers() {
                try {
                        System.out.println("Obteniendo usuarios de GraphQL nativo: " + graphqlCrmUrl);

                        Map<String, Object> requestBody = Map.of(
                                        "query", QueryDetails.GET_ALL_USERS_QUERY);

                        // Construir la request con autenticación OAuth2
                        var requestBuilder = restClient
                                        .post()
                                        .uri(graphqlCrmUrl + "/graphql")
                                        .body(requestBody)
                                        .header("Content-Type", "application/json");

                        // Obtener token OAuth2 del servidor de autorización
                        String oauth2Token = getOAuth2Token();
                        if (oauth2Token != null) {
                                System.out.println("✅ Token OAuth2 obtenido, enviando a GraphQL nativo");
                                System.out.println("🔗 URL destino: " + graphqlCrmUrl + "/graphql");
                                requestBuilder = requestBuilder.header("Authorization", "Bearer " + oauth2Token);
                        } else {
                                System.out.println("⚠️ No se pudo obtener token OAuth2");
                        }

                        @SuppressWarnings("unchecked")
                        Map<String, Object> response = requestBuilder
                                        .retrieve()
                                        .body(Map.class);

                        return extractUsersResponse(response);

                } catch (RestClientException e) {
                        System.err.println("Error de conexión GraphQL en getAllUsers: " + e.getMessage());
                        return createErrorUsersResponse("Error de conexión con el servidor GraphQL: " + e.getMessage());
                } catch (Exception e) {
                        System.err.println("Error inesperado en getAllUsers: " + e.getMessage());
                        e.printStackTrace();
                        return createErrorUsersResponse("Error inesperado: " + e.getMessage());
                }
        }

        /**
         * Obtiene el token desde el header HTTP del frontend
         */
        private String getTokenFromRequest() {
                try {
                        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                                        .getRequestAttributes();
                        if (attributes != null) {
                                HttpServletRequest request = attributes.getRequest();
                                String authHeader = request.getHeader("Authorization");

                                if (authHeader != null && authHeader.startsWith("Bearer ")) {
                                        String token = authHeader.substring(7); // Remover "Bearer "
                                        System.out.println("🔐 Token recibido desde frontend: ***"
                                                        + token.substring(Math.max(0, token.length() - 10)));
                                        return token;
                                }
                        }
                        return null;
                } catch (Exception e) {
                        System.err.println("Error al obtener token desde request: " + e.getMessage());
                        return null;
                }
        }

        /**
         * Método para logout (limpiar token)
         */
        public AuthResponse logout() {
                try {
                        authTokenService.clearToken();

                        AuthResponse response = new AuthResponse();
                        response.setSuccess(true);
                        response.setMessage("Logout exitoso");

                        return response;
                } catch (Exception e) {
                        System.err.println("Error en logout: " + e.getMessage());
                        return createErrorResponse("Error al cerrar sesión");
                }
        }

        /**
         * Métodos auxiliares para getAllUsers
         */
        private GetAllUsersResponse extractUsersResponse(Map<String, Object> response) {
                try {
                        // Debug: Imprimir respuesta completa
                        System.out.println("Respuesta completa de GraphQL: " + response);

                        @SuppressWarnings("unchecked")
                        Map<String, Object> data = (Map<String, Object>) response.get("data");
                        System.out.println("Data extraída: " + data);

                        if (data == null) {
                                System.err.println("Data es null - verificar errores en la respuesta");
                                @SuppressWarnings("unchecked")
                                List<Map<String, Object>> errors = (List<Map<String, Object>>) response.get("errors");
                                if (errors != null) {
                                        System.err.println("Errores GraphQL: " + errors);
                                }
                                return createErrorUsersResponse("No se recibieron datos del servidor GraphQL");
                        }

                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> usersData = (List<Map<String, Object>>) data.get("getAllUsers");
                        System.out.println("Users data extraída: " + usersData);

                        if (usersData == null) {
                                System.err.println("getAllUsers devolvió null - posible problema de autenticación");
                                return createErrorUsersResponse(
                                                "No se pudieron obtener los usuarios - verificar autenticación");
                        }

                        // Convertir a DTOs
                        List<UserResponse> users = usersData.stream()
                                        .map(this::mapToUserResponse)
                                        .toList();

                        GetAllUsersResponse getAllUsersResponse = new GetAllUsersResponse();
                        getAllUsersResponse.setUsers(users);
                        getAllUsersResponse.setSuccess(true);
                        getAllUsersResponse.setMessage("Usuarios obtenidos exitosamente");

                        return getAllUsersResponse;
                } catch (Exception e) {
                        System.err.println("Error al extraer respuesta de usuarios: " + e.getMessage());
                        return createErrorUsersResponse("Error al procesar respuesta del servidor");
                }
        }

        private UserResponse mapToUserResponse(Map<String, Object> userData) {
                UserResponse user = new UserResponse();
                user.setId((String) userData.get("id")); // Cambiado de uuid a id
                user.setUsername((String) userData.get("username"));
                user.setNombre((String) userData.get("nombre"));
                user.setApellido((String) userData.get("apellido"));
                user.setDni((String) userData.get("dni"));
                user.setTelefono((String) userData.get("telefono"));
                user.setEmail((String) userData.get("email"));
                user.setRole((String) userData.get("role"));
                user.setEstado((String) userData.get("estado"));
                // Nota: fechaCreacion se puede mapear si es necesario
                return user;
        }

        private GetAllUsersResponse createErrorUsersResponse(String message) {
                GetAllUsersResponse errorResponse = new GetAllUsersResponse();
                errorResponse.setSuccess(false);
                errorResponse.setMessage(message);
                return errorResponse;
        }

        /**
         * Métodos auxiliares para extraer respuestas y manejar errores
         */
        private AuthResponse extractAuthResponse(Map<String, Object> response, String operationType) {
                try {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> data = (Map<String, Object>) response.get("data");
                        @SuppressWarnings("unchecked")
                        Map<String, Object> operationData = (Map<String, Object>) data.get(operationType);

                        AuthResponse authResponse = new AuthResponse();
                        authResponse.setSuccess((Boolean) operationData.get("success"));
                        authResponse.setMessage((String) operationData.get("message"));

                        // Solo para login, extraer el token
                        if ("login".equals(operationType)) {
                                authResponse.setToken((String) operationData.get("token"));
                        }

                        return authResponse;
                } catch (Exception e) {
                        System.err.println("Error al extraer respuesta de " + operationType + ": " + e.getMessage());
                        return createErrorResponse("Error al procesar respuesta del servidor");
                }
        }

        private AuthResponse createErrorResponse(String message) {
                AuthResponse errorResponse = new AuthResponse();
                errorResponse.setSuccess(false);
                errorResponse.setMessage(message);
                return errorResponse;
        }

        /**
         * Obtiene token OAuth2 del servidor de autorización
         */
        private String getOAuth2Token() {
                try {
                        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
                        formData.add("grant_type", "client_credentials");
                        formData.add("scope", "openid");

                        var response = restClient.post()
                                        .uri("http://localhost:8099/oauth2/token")
                                        .body(formData)
                                        .header(HttpHeaders.AUTHORIZATION,
                                                        "Basic " + Base64.getEncoder()
                                                                        .encodeToString("client:secret".getBytes()))
                                        .header("Content-Type", "application/x-www-form-urlencoded")
                                        .retrieve()
                                        .body(AccessTokenResponse.class);

                        return response != null ? response.accessToken() : null;
                } catch (Exception e) {
                        System.err.println("Error al obtener token OAuth2: " + e.getMessage());
                        return null;
                }
        }

        private record AccessTokenResponse(
                        @JsonProperty("access_token") String accessToken,
                        @JsonProperty("token_type") String tokenType) {
        }
}
