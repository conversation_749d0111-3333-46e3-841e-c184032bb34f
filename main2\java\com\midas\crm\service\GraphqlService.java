package com.midas.crm.service;

import com.midas.crm.dto.request.LoginRequest;
import com.midas.crm.dto.request.RegisterRequest;
import com.midas.crm.dto.response.AuthResponse;
import com.midas.crm.dto.response.GetAllUsersResponse;
import com.midas.crm.dto.response.UserResponse;
import com.midas.crm.query.QueryDetails;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.RestClientException;

import java.util.List;
import java.util.Map;

@Service
public class GraphqlService {

        @Value("${graphql.crm.url}")
        private String graphqlCrmUrl;

        private final RestClient restClient;

        public GraphqlService() {
                this.restClient = RestClient.create();
        }

        /**
         * Método de login mejorado y limpio
         */

        public AuthResponse login(LoginRequest loginRequest) {
                try {
                        System.out.println("Conectando a GraphQL nativo en: " + graphqlCrmUrl);

                        Map<String, Object> variables = Map.of(
                                        "loginInput", Map.of(
                                                        "username", loginRequest.getUsername(),
                                                        "password", loginRequest.getPassword()));

                        Map<String, Object> requestBody = Map.of(
                                        "query", QueryDetails.LOGIN_MUTATION,
                                        "variables", variables);

                        @SuppressWarnings("unchecked")
                        Map<String, Object> response = restClient
                                        .post()
                                        .uri(graphqlCrmUrl + "/graphql")
                                        .body(requestBody)
                                        .header("Content-Type", "application/json")
                                        .retrieve()
                                        .body(Map.class);

                        return extractAuthResponse(response, "login");

                } catch (RestClientException e) {
                        System.err.println("Error de conexión GraphQL: " + e.getMessage());
                        return createErrorResponse("Error de conexión con el servidor GraphQL: " + e.getMessage());
                } catch (Exception e) {
                        System.err.println("Error inesperado en login: " + e.getMessage());
                        e.printStackTrace();
                        return createErrorResponse("Error inesperado: " + e.getMessage());
                }
        }

        /**
         * Método de register mejorado y limpio
         */
        public AuthResponse register(RegisterRequest registerRequest) {
                try {
                        System.out.println("Registrando usuario en GraphQL nativo: " + graphqlCrmUrl);

                        Map<String, Object> variables = Map.of(
                                        "userInput", Map.of(
                                                        "username", registerRequest.getUsername(),
                                                        "password", registerRequest.getPassword(),
                                                        "nombre", registerRequest.getNombre(),
                                                        "apellido", registerRequest.getApellido(),
                                                        "dni", registerRequest.getDni(),
                                                        "telefono", registerRequest.getTelefono(),
                                                        "email", registerRequest.getEmail(),
                                                        "role", registerRequest.getRole()));

                        Map<String, Object> requestBody = Map.of(
                                        "query", QueryDetails.REGISTER_MUTATION,
                                        "variables", variables);

                        @SuppressWarnings("unchecked")
                        Map<String, Object> response = restClient
                                        .post()
                                        .uri(graphqlCrmUrl + "/graphql")
                                        .body(requestBody)
                                        .header("Content-Type", "application/json")
                                        .retrieve()
                                        .body(Map.class);

                        return extractAuthResponse(response, "register");

                } catch (RestClientException e) {
                        System.err.println("Error de conexión GraphQL en register: " + e.getMessage());
                        return createErrorResponse("Error de conexión con el servidor GraphQL: " + e.getMessage());
                } catch (Exception e) {
                        System.err.println("Error inesperado en register: " + e.getMessage());
                        e.printStackTrace();
                        return createErrorResponse("Error inesperado: " + e.getMessage());
                }
        }

        /**
         * Método para obtener todos los usuarios (requiere autenticación)
         */
        public GetAllUsersResponse getAllUsers() {
                try {
                        System.out.println("Obteniendo usuarios de GraphQL nativo: " + graphqlCrmUrl);

                        Map<String, Object> requestBody = Map.of(
                                        "query", QueryDetails.GET_ALL_USERS_QUERY);

                        @SuppressWarnings("unchecked")
                        Map<String, Object> response = restClient
                                        .post()
                                        .uri(graphqlCrmUrl + "/graphql")
                                        .body(requestBody)
                                        .header("Content-Type", "application/json")
                                        // TODO: Agregar token de autenticación cuando sea necesario
                                        // .header("Authorization", "Bearer " + token)
                                        .retrieve()
                                        .body(Map.class);

                        return extractUsersResponse(response);

                } catch (RestClientException e) {
                        System.err.println("Error de conexión GraphQL en getAllUsers: " + e.getMessage());
                        return createErrorUsersResponse("Error de conexión con el servidor GraphQL: " + e.getMessage());
                } catch (Exception e) {
                        System.err.println("Error inesperado en getAllUsers: " + e.getMessage());
                        e.printStackTrace();
                        return createErrorUsersResponse("Error inesperado: " + e.getMessage());
                }
        }

        /**
         * Métodos auxiliares para getAllUsers
         */
        private GetAllUsersResponse extractUsersResponse(Map<String, Object> response) {
                try {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> data = (Map<String, Object>) response.get("data");
                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> usersData = (List<Map<String, Object>>) data.get("getAllUsers");

                        // Convertir a DTOs
                        List<UserResponse> users = usersData.stream()
                                        .map(this::mapToUserResponse)
                                        .toList();

                        GetAllUsersResponse getAllUsersResponse = new GetAllUsersResponse();
                        getAllUsersResponse.setUsers(users);
                        getAllUsersResponse.setSuccess(true);
                        getAllUsersResponse.setMessage("Usuarios obtenidos exitosamente");

                        return getAllUsersResponse;
                } catch (Exception e) {
                        System.err.println("Error al extraer respuesta de usuarios: " + e.getMessage());
                        return createErrorUsersResponse("Error al procesar respuesta del servidor");
                }
        }

        private UserResponse mapToUserResponse(Map<String, Object> userData) {
                UserResponse user = new UserResponse();
                user.setId((String) userData.get("id")); // Cambiado de uuid a id
                user.setUsername((String) userData.get("username"));
                user.setNombre((String) userData.get("nombre"));
                user.setApellido((String) userData.get("apellido"));
                user.setDni((String) userData.get("dni"));
                user.setTelefono((String) userData.get("telefono"));
                user.setEmail((String) userData.get("email"));
                user.setRole((String) userData.get("role"));
                user.setEstado((String) userData.get("estado"));
                // Nota: fechaCreacion se puede mapear si es necesario
                return user;
        }

        private GetAllUsersResponse createErrorUsersResponse(String message) {
                GetAllUsersResponse errorResponse = new GetAllUsersResponse();
                errorResponse.setSuccess(false);
                errorResponse.setMessage(message);
                return errorResponse;
        }

        /**
         * Métodos auxiliares para extraer respuestas y manejar errores
         */
        private AuthResponse extractAuthResponse(Map<String, Object> response, String operationType) {
                try {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> data = (Map<String, Object>) response.get("data");
                        @SuppressWarnings("unchecked")
                        Map<String, Object> operationData = (Map<String, Object>) data.get(operationType);

                        AuthResponse authResponse = new AuthResponse();
                        authResponse.setSuccess((Boolean) operationData.get("success"));
                        authResponse.setMessage((String) operationData.get("message"));

                        // Solo para login, extraer el token
                        if ("login".equals(operationType)) {
                                authResponse.setToken((String) operationData.get("token"));
                        }

                        return authResponse;
                } catch (Exception e) {
                        System.err.println("Error al extraer respuesta de " + operationType + ": " + e.getMessage());
                        return createErrorResponse("Error al procesar respuesta del servidor");
                }
        }

        private AuthResponse createErrorResponse(String message) {
                AuthResponse errorResponse = new AuthResponse();
                errorResponse.setSuccess(false);
                errorResponse.setMessage(message);
                return errorResponse;
        }
}
