package com.midas.graphqlcrm.service;

import com.midas.graphqlcrm.dto.AuthResponse;
import com.midas.graphqlcrm.dto.LoginRequest;
import com.midas.graphqlcrm.dto.RegisterRequest;
import com.midas.graphqlcrm.entity.Role;
import com.midas.graphqlcrm.entity.User;
import com.midas.graphqlcrm.repository.UserRepository;
import com.midas.graphqlcrm.search.user.UserSearchQuery;
import com.midas.graphqlcrm.util.MapperUtil;
import com.zee.graphqlcrm.codegen.types.UserSearchInput;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuthService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final MapperUtil mapperUtil;
    private final UserSearchQuery userSearchQuery;

    // Sink para WebSocket subscription
    private final Sinks.Many<List<com.zee.graphqlcrm.codegen.types.User>> userListSink = Sinks.many().multicast()
            .onBackpressureBuffer();

    private final Sinks.Many<com.zee.graphqlcrm.codegen.types.User> userCreatedSink = Sinks.many().multicast()
            .onBackpressureBuffer();
    private final Sinks.Many<com.zee.graphqlcrm.codegen.types.User> userUpdatedSink = Sinks.many().multicast()
            .onBackpressureBuffer();

    public AuthResponse register(RegisterRequest request) {
        try {
            // Validaciones de unicidad
            if (userRepository.existsByUsername(request.getUsername())) {
                return AuthResponse.builder()
                        .success(false)
                        .message("El nombre de usuario ya existe")
                        .build();
            }

            if (userRepository.existsByDni(request.getDni())) {
                return AuthResponse.builder()
                        .success(false)
                        .message("El DNI ya está registrado")
                        .build();
            }

            if (request.getEmail() != null && !request.getEmail().isEmpty() &&
                    userRepository.existsByEmail(request.getEmail())) {
                return AuthResponse.builder()
                        .success(false)
                        .message("El email ya está registrado")
                        .build();
            }

            // Crear entidad User
            User user = new User();
            user.setUsername(request.getUsername());
            user.setPassword(passwordEncoder.encode(request.getPassword()));
            user.setNombre(request.getNombre());
            user.setApellido(request.getApellido());
            user.setDni(request.getDni());
            user.setTelefono(request.getTelefono());
            user.setEmail(request.getEmail() != null && !request.getEmail().isEmpty()
                    ? request.getEmail()
                    : request.getUsername() + "@midas.pe");
            user.setRole(request.getRole() != null ? request.getRole() : Role.ASESOR);
            user.setEstado("A");
            user.setFechaCreacion(LocalDateTime.now());

            // Guardar en la base de datos
            User savedUser = userRepository.save(user);

            // Mapear a DTO para emitir por WebSocket
            com.zee.graphqlcrm.codegen.types.User userDto = mapperUtil.mapToUserDto(savedUser);

            userCreatedSink.tryEmitNext(userDto); // luego de crear usuario
            // Emitir lista completa actualizada
            userListSink.tryEmitNext(getAllUsersGraphQL());

            // Devolver respuesta API REST
            return AuthResponse.builder()
                    .success(true)
                    .message("Usuario registrado exitosamente")
                    .user(AuthResponse.UserInfo.builder()
                            .id(savedUser.getId())
                            .username(savedUser.getUsername())
                            .nombre(savedUser.getNombre())
                            .apellido(savedUser.getApellido())
                            .dni(savedUser.getDni())
                            .email(savedUser.getEmail())
                            .role(savedUser.getRole())
                            .estado(savedUser.getEstado())
                            .build())
                    .build();

        } catch (Exception e) {
            log.error("Error al registrar usuario: ", e);
            return AuthResponse.builder()
                    .success(false)
                    .message("Error interno del servidor")
                    .build();
        }
    }

    public AuthResponse login(LoginRequest request) {
        try {
            log.info("Iniciando proceso de login para usuario: {}", request.getUsername());

            Optional<User> userOpt = userRepository.findByUsername(request.getUsername());
            if (userOpt.isEmpty()) {
                log.warn("Usuario no encontrado: {}", request.getUsername());
                return AuthResponse.builder()
                        .success(false)
                        .message("Usuario o contraseña incorrectos")
                        .build();
            }

            User user = userOpt.get();
            log.info("Usuario encontrado: {} con estado: {}", user.getUsername(), user.getEstado());

            if (!"A".equals(user.getEstado())) {
                log.warn("Usuario inactivo intentando hacer login: {}", request.getUsername());
                return AuthResponse.builder()
                        .success(false)
                        .message("Usted no tiene accesos al sistema. Su cuenta está inactiva.")
                        .build();
            }

            if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
                return AuthResponse.builder()
                        .success(false)
                        .message("Usuario o contraseña incorrectos")
                        .build();
            }

            String token = getOAuth2Token();
            if (token == null) {
                return AuthResponse.builder()
                        .success(false)
                        .message("Error al obtener token de autenticación")
                        .build();
            }

            return AuthResponse.builder()
                    .success(true)
                    .message("Login exitoso")
                    .token(token)
                    .user(AuthResponse.UserInfo.builder()
                            .id(user.getId())
                            .username(user.getUsername())
                            .nombre(user.getNombre())
                            .apellido(user.getApellido())
                            .dni(user.getDni())
                            .email(user.getEmail())
                            .role(user.getRole())
                            .estado(user.getEstado())
                            .build())
                    .build();
        } catch (Exception e) {
            log.error("Error al hacer login: ", e);
            return AuthResponse.builder()
                    .success(false)
                    .message("Error interno del servidor")
                    .build();
        }
    }

    public AuthResponse changeUserStatus(String username, String newStatus) {
        try {
            Optional<User> userOpt = userRepository.findByUsername(username);
            if (userOpt.isEmpty()) {
                return AuthResponse.builder()
                        .success(false)
                        .message("Usuario no encontrado")
                        .build();
            }

            User user = userOpt.get();
            user.setEstado(newStatus);
            userRepository.save(user);

            userUpdatedSink.tryEmitNext(mapperUtil.mapToUserDto(user));
            userListSink.tryEmitNext(getAllUsersGraphQL());

            return AuthResponse.builder()
                    .success(true)
                    .message("Estado del usuario actualizado a: " + ("A".equals(newStatus) ? "Activo" : "Inactivo"))
                    .user(AuthResponse.UserInfo.builder()
                            .id(user.getId())
                            .username(user.getUsername())
                            .nombre(user.getNombre())
                            .apellido(user.getApellido())
                            .dni(user.getDni())
                            .email(user.getEmail())
                            .role(user.getRole())
                            .estado(user.getEstado())
                            .build())
                    .build();
        } catch (Exception e) {
            log.error("Error al cambiar estado del usuario: ", e);
            return AuthResponse.builder()
                    .success(false)
                    .message("Error interno del servidor")
                    .build();
        }
    }

    public com.zee.graphqlcrm.codegen.types.LoginResponse loginGraphQL(
            com.zee.graphqlcrm.codegen.types.LoginInput input) {
        try {
            AuthResponse authResponse = loginREST(input.getUsername(), input.getPassword());

            if (!authResponse.isSuccess()) {
                return com.zee.graphqlcrm.codegen.types.LoginResponse.newBuilder()
                        .success(false)
                        .message(authResponse.getMessage())
                        .build();
            }

            User user = userRepository.findByUsername(input.getUsername()).orElse(null);
            if (user == null) {
                return com.zee.graphqlcrm.codegen.types.LoginResponse.newBuilder()
                        .success(false)
                        .message("Usuario no encontrado")
                        .build();
            }

            com.zee.graphqlcrm.codegen.types.UserLoginData.Builder userDataBuilder = com.zee.graphqlcrm.codegen.types.UserLoginData
                    .newBuilder()
                    .id(user.getId().toString())
                    .username(user.getUsername())
                    .nombre(user.getNombre())
                    .apellido(user.getApellido())
                    .dni(user.getDni())
                    .telefono(user.getTelefono())
                    .email(user.getEmail())
                    .role(com.zee.graphqlcrm.codegen.types.Role.valueOf(user.getRole().name()))
                    .estado(user.getEstado());

            loadAdditionalDataByRole(user, userDataBuilder);

            return com.zee.graphqlcrm.codegen.types.LoginResponse.newBuilder()
                    .success(true)
                    .message("Login exitoso")
                    .token(authResponse.getToken())
                    .user(userDataBuilder.build())
                    .build();
        } catch (Exception e) {
            log.error("Error en loginGraphQL: ", e);
            return com.zee.graphqlcrm.codegen.types.LoginResponse.newBuilder()
                    .success(false)
                    .message("Error interno del servidor")
                    .build();
        }
    }

    public com.zee.graphqlcrm.codegen.types.RegisterResponse registerGraphQL(
            com.zee.graphqlcrm.codegen.types.UserInput input) {
        try {
            User savedUser = userRepository.save(mapperUtil.mapToUserEntity(input));

            com.zee.graphqlcrm.codegen.types.User userDto = mapperUtil.mapToUserDto(savedUser);

            // Emitir lista completa actualizada via WebSocket para getAllUsers
            userListSink.tryEmitNext(getAllUsersGraphQL());
            log.info("Lista de usuarios actualizada via WebSocket después del registro de: {}",
                    savedUser.getUsername());

            return com.zee.graphqlcrm.codegen.types.RegisterResponse.newBuilder()
                    .success(true)
                    .message("Usuario registrado exitosamente")
                    .user(userDto)
                    .build();
        } catch (Exception e) {
            log.error("Error en registerGraphQL: ", e);
            return com.zee.graphqlcrm.codegen.types.RegisterResponse.newBuilder()
                    .success(false)
                    .message("Error interno del servidor")
                    .build();
        }
    }

    public com.zee.graphqlcrm.codegen.types.User getUserGraphQLDto(String username) {
        User user = userRepository.findByUsername(username).orElse(null);
        return user != null ? mapperUtil.mapToUserDto(user) : null;
    }

    public List<com.zee.graphqlcrm.codegen.types.User> getAllUsersGraphQL() {
        return userRepository.findAll().stream()
                .map(mapperUtil::mapToUserDto)
                .collect(java.util.stream.Collectors.toList());
    }

    // Nuevos métodos de búsqueda con programación funcional
    public List<com.zee.graphqlcrm.codegen.types.User> searchUsersGraphQL(UserSearchInput input) {
        return Optional.ofNullable(input)
                .map(userSearchQuery::buildUserSearchParams)
                .map(userRepository::findAll)
                .orElse(java.util.Collections.emptyList())
                .stream()
                .map(mapperUtil::mapToUserDto)
                .peek(user -> log.debug("Usuario encontrado: {}", user.getUsername()))
                .collect(java.util.stream.Collectors.toList());
    }

    public com.zee.graphqlcrm.codegen.types.User getUserByIdGraphQL(Long id) {
        return Optional.ofNullable(id)
                .flatMap(userRepository::findById)
                .map(mapperUtil::mapToUserDto)
                .orElse(null);
    }

    // Métodos REST (mantener para compatibilidad)
    public AuthResponse loginREST(String username, String password) {
        LoginRequest request = new LoginRequest();
        request.setUsername(username);
        request.setPassword(password);
        return login(request);
    }

    public AuthResponse registerGraphQL(RegisterRequest request) {
        return register(request);
    }

    public User getUserByUsername(String username) {
        return userRepository.findByUsername(username).orElse(null);
    }

    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    public Sinks.Many<com.zee.graphqlcrm.codegen.types.User> userCreatedSink() {
        return userCreatedSink;
    }

    public Sinks.Many<com.zee.graphqlcrm.codegen.types.User> userUpdatedSink() {
        return userUpdatedSink;
    }

    // Flux para lista completa de usuarios actualizada
    public Flux<List<com.zee.graphqlcrm.codegen.types.User>> userListFlux() {
        return userListSink.asFlux()
                .doOnNext(list -> log.info("Lista de usuarios actualizada via WebSocket: {} usuarios", list.size()));
    }

    /**
     * Carga datos adicionales según el rol del usuario
     */
    @Transactional(readOnly = true)
    private void loadAdditionalDataByRole(User user, Object userDataBuilder) {
        try {
            log.info("Cargando datos adicionales para usuario: {} con rol: {}", user.getUsername(), user.getRole());

            if (user.getRole() == Role.COORDINADOR) {
                log.info("Usuario es COORDINADOR - cargando asesores");
                List<User> asesores = userRepository.findByCoordinadorIdAndRole(user.getId(), Role.ASESOR);
                log.info("Coordinador tiene {} asesores asignados", asesores.size());
                // TODO: agregar asesores al builder
            } else if (user.getRole() == Role.ASESOR) {
                log.info("Usuario es ASESOR - cargando coordinador");
                User userWithCoordinador = userRepository.findByIdWithCoordinador(user.getId());
                if (userWithCoordinador != null && userWithCoordinador.getCoordinador() != null) {
                    log.info("Asesor tiene coordinador asignado: {}",
                            userWithCoordinador.getCoordinador().getUsername());
                    // TODO: agregar coordinador al builder
                } else {
                    log.info("Asesor no tiene coordinador asignado");
                }
            }
        } catch (Exception e) {
            log.error("Error al cargar datos adicionales por rol: ", e);
        }
    }

    private String getOAuth2Token() {
        try {
            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.add("grant_type", "client_credentials");
            formData.add("scope", "openid");

            RestClient restClient = RestClient.create();

            var response = restClient.post()
                    .uri("http://localhost:8099/oauth2/token")
                    .body(formData)
                    .header(HttpHeaders.AUTHORIZATION,
                            "Basic " + Base64.getEncoder().encodeToString("client:secret".getBytes()))
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .retrieve()
                    .body(TokenResponse.class);

            return response != null ? response.access_token : null;
        } catch (Exception e) {
            log.error("Error al obtener token OAuth2: ", e);
            return null;
        }
    }

    private static class TokenResponse {
        public String access_token;
        public String token_type;
    }

}
